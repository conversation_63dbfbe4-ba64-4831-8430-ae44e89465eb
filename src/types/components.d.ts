/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by vite-plugin-uni-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AddressItem: typeof import('./../components/address/address-item.vue')['default']
    CardQuota: typeof import('./../components/card-quota.vue')['default']
    GoodsItem: typeof import('./../components/goods-item.vue')['default']
    GoodsRecommendation: typeof import('./../components/goods-recommendation.vue')['default']
    GoodsRecommendationList: typeof import('./../components/goods-recommendation-list.vue')['default']
    RepaymentPlan: typeof import('./../components/repayment-plan.vue')['default']
    SearchBox: typeof import('./../components/SearchBox.vue')['default']
    ShopTitle: typeof import('./../components/shop-title.vue')['default']
    WdConfigProvider: typeof import('wot-design-uni/components/wd-config-provider/wd-config-provider.vue')['default']
    WdNavbar: typeof import('wot-design-uni/components/wd-navbar/wd-navbar.vue')['default']
  }
}
