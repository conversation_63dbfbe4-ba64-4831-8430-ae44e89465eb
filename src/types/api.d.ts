/// <reference types="types" />

declare namespace API {
  /** 埋点上报 */
  export interface EventTrackingParams {
    accessPath: string
    browser: string
    channel: string
    device: string
    eid: string
    eventType: string
    extend1: string
    extra: string
    jv: string
    language: string
    os: string
    pageUrl: string
    pageX: string
    pageY: string
    pv: string
    referer: string
    remark: string
    screen: string
    time: string
    vid: string
    [property: string]: any
  }

  /**
   * 协议类型代码
   * @property P01 - 登录隐私保护政策
   * @property P02 - 隐私政策
   * @property P03 - 支付条款
   * @property P04 - 会员规则
   */
  export type AgreementType = 'P01' | 'P02' | 'P03' | 'P04' | 'P20'

  // 用户相关接口
  namespace User {
    interface LoginParams {
      username: string
      password: string
    }

    interface LoginResponse {
      token: string
      userInfo: {
        id: string
        name: string
        avatar?: string
      }
    }

    // 手机验证码登录参数
    interface PhoneLoginParams {
      phone: string
      code: string
      source?: 'app' | 'h5'
    }

    // 登录响应
    interface PhoneLoginResponse {
      refreshToken: string
      accessToken: string
      userInfo?: {
        id: string
        name: string
        phone: string
        avatar?: string
      }
    }

    // 获取验证码参数
    interface CaptchaParams {
      phone: string
      type: 'login' | 'logout'
    }

    // 获取验证码响应
    interface CaptchaResponse {
      success: boolean
      message?: string
    }
  }

  // 商品相关接口
  namespace Goods {
    interface ListParams {
      /**
       * 商品分类id
       */
      category_id?: number
      /**
       * 是否热门
       */
      is_hot?: number
      /**
       * 商品分类级别
       */
      level?: number
      /**
       * 排序方式：默认：DESC
       */
      order?: string
      page?: number
      size?: number
      /**
       * 排序字段，默认：created_at
       */
      sort?: string
      [property: string]: any
    }

    interface GoodsItem {
      id: string
      title: string
      price: number
      thumbnail: string
    }

    type ListResponse = {
      total: number
      list: GoodsItem[]
    }
  }

  // 购物车相关接口
  namespace Cart {
    // 添加购物车参数
    interface AddToCartParams {
      product_id: string | number
      quantity: number
    }

    // 更新购物车数量参数
    interface UpdateCartParams {
      cart_id: string | number
      quantity: number
    }

    // 删除购物车参数
    interface RemoveFromCartParams {
      cart_ids: Array<string | number>
    }

    // 购物车商品项
    interface CartItem {
      id: string | number
      product_id: string | number
      name: string
      price: number
      quantity: number
      image: string
      selected: boolean
      [key: string]: any
    }

    // 购物车列表响应
    interface CartListResponse {
      items: CartItem[]
      total_price: number
      total_quantity: number
    }

    // 购物车数量响应
    interface CartCountResponse {
      count: number
    }
  }
}
