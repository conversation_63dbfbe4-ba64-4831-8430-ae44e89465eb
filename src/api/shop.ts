/**
 * 商品/购物接口
 */
import http from '@/http/requestWrapper'

/** 获取商品分类列表 */
export const getGoodsCategoryListApi = (data: { type: 'home' | 'all' }) => {
  return http.get(`/category/${data.type}`)
}

/** 获取商品列表 */
export const getGoodsListApi = (data: API.Goods.ListParams) => {
  return http.get(`/product/getProductList`, { data })
}

/** 获取商品详情 */
export const getGoodsDetailApi = (data: { id: string | number }) => {
  return http.get(`/product/${data.id}`)
}

/** 商品关键字搜索 */
export const getGoodsSearchApi = (data: {
  keyword: string
  page: number
  size: number
  sort?: 'created_at' | 'price' | 'sales'
  order?: 'DESC' | 'ASC'
}) => {
  return http.get(`/product/searchProducts`, { data })
}

/** 获取商品推荐列表 */
export const getGoodsRecommendListApi = (data: { page: number; size: number }) => {
  return http.get(`/product/recommend`, { data })
}

/** 商品加入购物车 */
export const addToCartApi = (data: { product_id: string | number; quantity: number; sku_id: number }) => {
  return http.post(`/cart/items`, { data })
}

/** 获取购物车商品数量 */
export const getCartQuantityApi = () => {
  return http.get('/cart/count')
}

/** 更新购物车商品数量 */
export const updateCartQuantityApi = (data: { cart_id: string | number; quantity: number }) => {
  return http.put(`/cart/items/${data.cart_id}`, { data })
}

/** 从购物车中移除商品 */
export const removeFromCartApi = (data: { cart_ids: Array<string | number> }) => {
  return http.delete(`/cart/items`, { data })
}

/** 清空购物车 */
export const clearCartApi = () => {
  return http.delete('/cart')
}

/** 获取购物车列表 */
export const getCartListApi = () => {
  return http.get('/cart')
}
