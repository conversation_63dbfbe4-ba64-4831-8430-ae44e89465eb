import http from '@/http/requestWrapper'

/** GET 请求测试 */
export const getTestApi = (query?: any) => {
  return http.get('/getTest', { query })
}

/** POST 请求测试 */
export const postTestApi = (data?: any, query?: any) => {
  return http.post('/postTest', { data, query })
}

/* await-to-js 简洁用法 */
export const awaitToJsTestApi = (query?: any) => {
  return http.post('/postTest', { query })
}

/** 分页 请求测试 */
export const getUserListApi = (query?: any) => {
  return http.get('/testList', { query })
}

/** 长时间请求 请求测试 */
export const getLongRequestApi = () => {
  return http.get('/longRequest')
}
