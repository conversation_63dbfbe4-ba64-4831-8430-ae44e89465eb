import { useCommonStore } from '@/store/common'
import { sendTrackingApi } from '@/api/common'
import { useToast } from 'wot-design-uni'
import type { ToastOptions } from 'wot-design-uni/components/wd-toast/types'

export type ToastType = 'show' | 'success' | 'error' | 'info' | 'warning' | 'loading'
export type ToastOptionsWithType = ToastOptions & {
  type?: ToastType
}

/**
 * 全局工具类
 */
class Utils {
  /**
   * 系统埋点
   * @param params 埋点参数
   * @returns 埋点数据对象或undefined
   * @example Utils.eventTracking({ eventType: 'page', remark: '进入首页' })
   */
  static eventTracking(params: Partial<API.EventTrackingParams>): Partial<API.EventTrackingParams> | undefined {
    try {
      const commonStore = useCommonStore()
      const { systemInfo } = commonStore
      const now = new Date()

      // 默认参数
      const defaultParams: Partial<API.EventTrackingParams> = {
        ...systemInfo,
        time: now.toISOString(),
        pageUrl: getCurrentPages().length > 0 ? getCurrentPages()[getCurrentPages().length - 1].route : '',
        referer: getCurrentPages().length > 1 ? getCurrentPages()[getCurrentPages().length - 2].route : '',
        eid: params?.eid || params?.remark
      }

      // 合并参数
      const trackingData = { ...defaultParams, ...params }

      // 上报数据
      sendTrackingApi(trackingData as API.EventTrackingParams).catch(err => {
        console.error('埋点上报失败:', err)
      })

      // 开发环境打印埋点数据
      if (import.meta.env.DEV) {
        console.log('埋点数据:', trackingData)
      }

      return trackingData
    } catch (error) {
      console.error('埋点上报异常:', error)
      return undefined
    }
  }

  /**
   * 手机号脱敏中间4位*号
   */
  static phoneMask(phone: string): string {
    return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
  }

  /**
   * Toast 轻提示
   * @example Utils.toast({ type: 'success', msg: '操作成功' })
   * @example Utils.toast({ type: 'error', msg: '操作失败' })
   * @example Utils.toast({ type: 'warning', msg: '警告信息' })
   * @example Utils.toast({ type: 'info', msg: '提示信息' })
   * @example Utils.toast({ type: 'loading', msg: '加载中' })
   * @example Utils.toast({ type: 'show', msg: '自定义提示' })
   * @example Utils.toast('简单提示') // 默认使用 show 方法
   */
  static toast(options: ToastOptionsWithType | string): void {
    try {
      const toast = useToast()
      const defaultOptions: ToastOptionsWithType = {
        direction: 'vertical',
        duration: 2000,
        iconSize: 37
      }

      let merged: ToastOptionsWithType
      if (typeof options === 'string') {
        merged = { ...defaultOptions, msg: options }
      } else {
        merged = { ...defaultOptions, ...options }
      }

      const { type = 'show', ...restOptions } = merged

      if (typeof toast[type] === 'function') {
        toast[type](restOptions)
      } else {
        toast.show(restOptions)
      }
    } catch (err) {
      // console.error('Toast 轻提示异常:', err)
      const optionsObj = typeof options === 'object' && options !== null ? options : {}
      switch (optionsObj?.type) {
        case 'success':
          uni.showToast({
            title: optionsObj.msg,
            image: '/static/images/toast_success.svg',
            duration: 2000
          })
          break
        case 'error':
          uni.showToast({
            title: optionsObj.msg,
            image: '/static/images/toast_error.svg',
            duration: 2000
          })
          break
        case 'loading':
          uni.showToast({
            title: optionsObj.msg,
            image: '/static/images/toast_loading.svg'
          })
          break
        default:
          uni.showToast({
            title: optionsObj.msg,
            icon: 'none',
            duration: 2000
          })
          break
      }
    }
  }
}

export default Utils
