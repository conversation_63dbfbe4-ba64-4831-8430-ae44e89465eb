{"pages": [{"path": "pages/tab/home/<USER>", "type": "home", "layout": "theme", "style": {"navigationStyle": "custom", "navigationBarTitleText": "首页"}}, {"path": "pages/loan/auth", "type": "page", "layout": "default", "style": {"navigationBarTitleText": "身份认证", "navigationStyle": "default", "app-plus": {"scrollIndicator": "none"}}}, {"path": "pages/loan/contact", "type": "page", "layout": "theme", "style": {"navigationBarTitleText": "联系人信息", "navigationStyle": "default"}}, {"path": "pages/loan/face", "type": "page"}, {"path": "pages/loan/info", "type": "page", "layout": "theme", "style": {"navigationBarTitleText": "基本信息", "navigationStyle": "default"}}, {"path": "pages/loan/result", "type": "page", "layout": "theme", "style": {"navigationBarTitleText": "审核结果", "navigationStyle": "default"}}, {"path": "pages/shop/activity", "type": "page", "layout": "theme", "style": {"navigationStyle": "custom"}}, {"path": "pages/shop/category", "type": "page", "layout": "theme", "style": {"navigationBarTitleText": "全部分类"}}, {"path": "pages/shop/order-confirm", "type": "page", "layout": "theme", "style": {"navigationBarTitleText": "确认订单", "navigationStyle": "default"}}, {"path": "pages/shop/order-detail", "type": "page", "layout": false, "style": {"navigationBarTitleText": "订单详情", "navigationStyle": "custom", "customNav": true}}, {"path": "pages/shop/order-list", "type": "page", "layout": "theme", "style": {"navigationBarTitleText": "我的订单"}}, {"path": "pages/shop/payment-result", "type": "page", "style": {"navigationBarTitleText": "支付结果", "navigationStyle": "default"}}, {"path": "pages/shop/payment", "type": "page", "layout": "theme", "style": {"navigationBarTitleText": "确认支付", "navigationStyle": "default"}}, {"path": "pages/shop/shop-detail", "type": "page", "layout": "theme", "style": {"navigationBarTitleText": "商品详情", "navigationStyle": "custom"}}, {"path": "pages/shop/shop-list", "type": "page", "layout": "theme", "style": {"navigationBarTitleText": "商品列表", "navigationStyle": "custom"}}, {"path": "pages/user/address-edit", "type": "page", "layout": "theme", "style": {"navigationBarTitleText": "编辑地址", "navigationStyle": "default"}}, {"path": "pages/user/address-list", "type": "page", "layout": "theme", "style": {"navigationBarTitleText": "我的地址", "navigationStyle": "default"}}, {"path": "pages/user/bank-card-add", "type": "page", "layout": "theme", "style": {"navigationBarTitleText": "银行卡绑定", "navigationStyle": "default"}}, {"path": "pages/user/bank-card", "type": "page", "layout": "theme", "style": {"navigationBarTitleText": "银行卡", "navigationStyle": "default"}}, {"path": "pages/common/404/index", "type": "page", "layout": "theme", "style": {"navigationStyle": "custom"}}, {"path": "pages/common/login/index", "type": "page", "layout": "theme", "style": {"navigationStyle": "custom", "navigationBarTitleText": "登录"}}, {"path": "pages/common/webview/index", "type": "page", "layout": "theme", "style": {}}, {"path": "pages/tab/shop/index", "type": "tab", "layout": "theme", "style": {"navigationStyle": "custom", "navigationBarTitleText": "购物车"}}, {"path": "pages/tab/user/index", "type": "tab", "layout": "theme", "style": {"navigationStyle": "custom", "navigationBarTextStyle": "white", "navigationBarTitleText": "我的"}}], "tabBar": {"color": "#D9D9D9", "selectedColor": "#FF3C29", "borderStyle": "black", "backgroundColor": "#ffffff", "iconWidth": "22px", "list": [{"pagePath": "pages/tab/home/<USER>", "iconPath": "static/images/tabbar/home.png", "selectedIconPath": "static/images/tabbar/home-select.png", "text": "首页"}, {"pagePath": "pages/tab/shop/index", "iconPath": "static/images/tabbar/shop.png", "selectedIconPath": "static/images/tabbar/shop-select.png", "text": "商城"}, {"pagePath": "pages/tab/user/index", "iconPath": "static/images/tabbar/user.png", "selectedIconPath": "static/images/tabbar/user-select.png", "text": "我的"}]}, "globalStyle": {"navigationBarTextStyle": "@navTxtStyle", "navigationBarBackgroundColor": "@navBgColor", "backgroundColor": "@bgColor", "enablePullDownRefresh": false, "onReachBottomDistance": 5000, "app-plus": {"scrollIndicator": "none"}, "animationType": "slide-in-right"}, "easycom": {"autoscan": true, "custom": {"^wd-(.*)": "wot-design-uni/components/wd-$1/wd-$1.vue", "^(?!z-paging-refresh|z-paging-load-more)z-paging(.*)": "z-paging/components/z-paging$1/z-paging$1.vue"}}, "__esModule": true, "subPackages": []}