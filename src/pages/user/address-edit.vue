<route lang="json5" type="page">
{
  layout: 'theme',
  style: {
    navigationBarTitleText: '编辑地址',
    navigationStyle: 'default'
  }
}
</route>

<template>
  <view class="min-h-100vh bg-[#F8F8F8]">
    <view class="px-30rpx py-30rpx text-28rpx text-[#222]">
      <!-- 表单区域 -->
      <view class="mb-20rpx rounded-20rpx bg-white px-30rpx">
        <wd-cell-group>
          <!-- 姓名 -->
          <wd-input
            v-model="formData.name"
            label="姓名"
            label-width="172rpx"
            placeholder="请输入收货人姓名"
            clearable
            :custom-class="formItemClass"
          />

          <!-- 手机号 -->
          <wd-input
            v-model="formData.phone"
            label="手机号"
            label-width="172rpx"
            placeholder="请输入手机号"
            type="number"
            :maxlength="11"
            clearable
            :custom-class="formItemClass"
          />

          <!-- 所在地区 - 三级联动选择器 -->
          <wd-picker
            v-model="regionValue"
            :columns="regionColumns"
            label="所在地区"
            label-width="172rpx"
            placeholder="请选择所在地区"
            :column-change="onChangeDistrict"
            :display-format="displayFormat"
            @confirm="confirmRegion"
            :custom-class="formItemClass"
          />

          <!-- 详细地址 -->
          <wd-input
            v-model="formData.detailAddress"
            label="详细地址"
            label-width="172rpx"
            placeholder="请输入详细地址，如街道、小区、楼栋号、单元室等"
            clearable
            show-word-limit
          />
        </wd-cell-group>
      </view>

      <!-- 设为默认 -->
      <view class="ml-20rpx mt-60rpx flex items-center" @click="toggleDefault">
        <wd-checkbox checked-color="var(--primary-color)" v-model="formData.isDefault" />
        <text class="text-28rpx text-[#333]">设为默认收货地址</text>
      </view>

      <!-- 保存按钮 -->
      <view class="mt-164rpx">
        <view
          v-if="fromPage === 'order'"
          class="m-(x-auto y-30rpx) h-44rpx w-546rpx rounded-12rpx bg-#000 p-(x-30rpx y-5rpx) text-center text-26rpx text-white leading-44rpx op-60"
        >
          请确认正确收货地址，确认订单后该地址不可修改！
        </view>
        <wd-button
          block
          :loading="loading"
          :disabled="!isFormValid"
          :custom-class="
            isFormValid ? 'btn-primary !rounded-full !h-90rpx' : 'btn-primary-disabled !rounded-full !h-90rpx'
          "
          @click="saveAddress"
        >
          保存
        </wd-button>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'

// 表单数据
const formData = reactive({
  id: '',
  name: '',
  phone: '',
  province: '',
  city: '',
  district: '',
  region: '',
  detailAddress: '',
  isDefault: false
})

// 加载状态
const loading = ref(false)

// 表单项样式
const formItemClass = 'border-b border-[#EFEFEF] py-6rpx'

// 表单验证
const isFormValid = computed(() => {
  return (
    formData.name.trim() !== '' &&
    /^1\d{10}$/.test(formData.phone) &&
    formData.region !== '' &&
    formData.detailAddress.trim() !== ''
  )
})

/**
 * 地区数据结构
 * key: 上级区域代码，'0'表示顶级（省份）
 * value: 该区域下级的列表，每项包含label和value
 */
const districtData = {
  // 省级数据
  '0': [
    { label: '北京', value: '110000' },
    { label: '广东省', value: '440000' }
  ],

  // 市级数据，按省级代码分组
  '110000': [{ label: '北京', value: '110100' }],
  '440000': [
    { label: '广州市', value: '440100' },
    { label: '韶关市', value: '440200' },
    { label: '深圳市', value: '440300' },
    { label: '珠海市', value: '440400' },
    { label: '汕头市', value: '440500' }
  ],

  // 区级数据，按市级代码分组
  '110100': [
    { label: '东城区', value: '110101' },
    { label: '西城区', value: '110102' },
    { label: '朝阳区', value: '110105' },
    { label: '丰台区', value: '110106' },
    { label: '石景山区', value: '110107' }
  ],
  '440100': [
    { label: '荔湾区', value: '440103' },
    { label: '越秀区', value: '440104' },
    { label: '海珠区', value: '440105' }
  ],
  '440200': [{ label: '武江区', value: '440203' }],
  '440300': [
    { label: '罗湖区', value: '440303' },
    { label: '福田区', value: '440304' }
  ],
  '440400': [
    { label: '香洲区', value: '440402' },
    { label: '斗门区', value: '440403' },
    { label: '金湾区', value: '440404' }
  ],
  '440500': [
    { label: '龙湖区', value: '440507' },
    { label: '金平区', value: '440511' }
  ]
}

// 选择器默认值 - [省, 市, 区]
const regionValue = ref(['110000', '110100', '110102'])

/**
 * 初始化三级联动的列数据
 * 第一列：省份列表
 * 第二列：根据第一列第一项决定的市级列表
 * 第三列：根据第二列第一项决定的区级列表
 */
const regionColumns = ref([
  districtData['0'], // 省级数据
  districtData[districtData['0'][0].value], // 市级数据，取第一个省的下级市
  districtData[districtData[districtData['0'][0].value][0].value] // 区级数据，取第一个市的下级区
])

/**
 * 处理地区选择器的列变化事件
 * @param pickerView 选择器实例，用于设置列数据
 * @param value 当前选择的所有列的值
 * @param columnIndex 当前变化的列索引
 * @param resolve 完成回调函数
 */
const onChangeDistrict = (pickerView, value, columnIndex, resolve) => {
  const item = value[columnIndex]

  // 如果是省份列变化
  if (columnIndex === 0) {
    // 更新市级列表
    pickerView.setColumnData(1, districtData[item.value])
    // 更新区级列表，取第一个市的下级区
    pickerView.setColumnData(2, districtData[districtData[item.value][0].value])
  }
  // 如果是市级列变化
  else if (columnIndex === 1) {
    // 更新区级列表
    pickerView.setColumnData(2, districtData[item.value])
  }

  resolve() // 完成列变化处理
}

/**
 * 显示格式化函数，将选中的项转为显示文本
 * @param items 选中的项
 * @return 格式化后的显示文本
 */
const displayFormat = items => {
  return items
    .map(item => {
      return item.label
    })
    .join(' ')
}

/**
 * 确认地区选择事件处理函数
 * @param event 事件对象，包含selectedValue和selectedLabel
 */
const confirmRegion = (event: any) => {
  const { selectedValue, selectedLabel } = event

  // 更新表单数据
  formData.province = selectedLabel[0]
  formData.city = selectedLabel[1]
  formData.district = selectedLabel[2]
  formData.region = `${selectedLabel[0]} ${selectedLabel[1]} ${selectedLabel[2]}`
}

// 切换默认地址状态
const toggleDefault = () => {
  formData.isDefault = !formData.isDefault
}

/**
 * 保存地址信息
 * 验证表单、生成完整地址并提交数据
 */
const saveAddress = () => {
  if (!isFormValid.value) {
    uni.showToast({
      title: '请完善信息',
      icon: 'none'
    })
    return
  }

  // 开始加载
  loading.value = true

  // 生成全地址
  const fullAddress = `${formData.region}${formData.detailAddress}`

  // 构建要保存的数据
  const addressData = {
    ...formData,
    fullAddress
  }

  // 模拟保存
  setTimeout(() => {
    loading.value = false
    uni.showToast({
      title: '保存成功',
      icon: 'success'
    })

    // 返回上一页
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }, 1000)
}

/**
 * 加载已有地址信息
 * @param id 地址ID
 */
const loadAddress = (id: string) => {
  // 模拟获取地址数据
  if (id === '1') {
    // 加载地址1
    Object.assign(formData, {
      id: '1',
      name: '王小花',
      phone: '17785245861',
      province: '四川省',
      city: '成都市',
      district: '金牛区',
      region: '四川省 成都市 金牛区',
      detailAddress: '街道小区-楼栋-单元-门牌号',
      isDefault: true
    })

    // 设置选择器的值
    regionValue.value = ['440000', '440100', '440103']
  } else if (id === '2') {
    // 加载地址2
    Object.assign(formData, {
      id: '2',
      name: '赵先生',
      phone: '17789089098',
      province: '北京市',
      city: '北京市',
      district: '海淀区',
      region: '北京市 北京市 海淀区',
      detailAddress: '中关村智能人才与产业创新基地',
      isDefault: false
    })

    // 设置选择器的值
    regionValue.value = ['110000', '110100', '110105']
  }
}

const fromPage = ref('')

// 页面加载
onLoad((options: any) => {
  if (options.id) {
    // 加载已有地址信息
    loadAddress(options.id)

    // 更新页面标题
    uni.setNavigationBarTitle({
      title: '编辑地址'
    })
  } else {
    // 新增地址
    uni.setNavigationBarTitle({
      title: '新增地址'
    })
  }

  if (options.from) {
    fromPage.value = options.from
  }
})
</script>

<style>
.btn-primary {
  color: white;
  background: linear-gradient(to right, #ff5d5d, #ff3c29);
}

.btn-primary-disabled {
  color: #999;
  background: #f5f5f5;
}

.region-value {
  font-size: 28rpx;
  color: #222;
}

.region-placeholder {
  font-size: 28rpx;
  color: #999;
}
</style>
