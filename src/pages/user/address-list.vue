<route lang="json5" type="page">
{
  layout: 'theme',
  style: {
    navigationBarTitleText: '我的地址',
    navigationStyle: 'default'
  }
}
</route>

<template>
  <view class="address-list-page box-border min-h-100vh flex flex-col bg-gray-100 px-30rpx pb-120rpx">
    <!-- 地址列表 -->
    <view v-if="addressList.length > 0">
      <view v-for="(item, index) in addressList" :key="index" class="mt-20rpx">
        <!-- 地址 -->
        <address-item
          :address="item"
          mode="manage"
          @click="handleAddressClick(item)"
          @edit="editAddress"
        ></address-item>
      </view>
    </view>

    <!-- 空状态 -->
    <view v-else class="flex-col-center flex-1 py-100rpx">
      <image
        src="https://cdn.jsdelivr.net/gh/eric-leo/static/common/images/empty.png"
        class="mb-30rpx h-200rpx w-200rpx"
        mode="aspectFit"
      ></image>
      <text class="text-28rpx text-[#999]">暂无收货地址</text>
      <text class="mt-10rpx text-26rpx text-[#BBBBBB]">请添加收货地址</text>
    </view>

    <!-- 底部添加按钮 -->
    <view class="fixed bottom-0 left-0 right-0 bg-white p-30rpx pb-[calc(var(--safe-area-bottom)+30rpx)] pb-safe">
      <wd-button
        icon="/static/images/user/add-address-icon.png"
        block
        style="background: linear-gradient(to right, #ff5d5d, #ff3c29)"
        @click="navigateToAddAddress"
      >
        添加收货地址
      </wd-button>
    </view>
  </view>
</template>

<script lang="ts" setup>
// import AddressItem from '@/components/address/address-item.vue'

// 地址列表
const addressList = ref([
  {
    id: '1',
    name: '王小花',
    phone: '17785245861',
    province: '四川省',
    city: '成都市',
    district: '金牛区',
    detailAddress: '街道小区-楼栋-单元-门牌号',
    fullAddress: '四川省成都市金牛区街道小区-楼栋-单元-门牌号',
    isDefault: true
  },
  {
    id: '2',
    name: '赵先生',
    phone: '17789089098',
    province: '北京市',
    city: '北京市',
    district: '海淀区',
    detailAddress: '中关村智能人才与产业创新基地',
    fullAddress: '北京市北京市海淀区中关村智能人才与产业创新基地',
    isDefault: false
  }
])

// 当前选中的地址（用于删除操作）
const currentAddress = ref(null)

// 从哪个页面跳转过来
const fromPage = ref('')

// 页面加载
onLoad(options => {
  if (options.from) {
    fromPage.value = options.from
  }
})

// 点击地址
const handleAddressClick = address => {
  // 如果是从订单页跳转过来，则选择地址后返回
  if (fromPage.value === 'order') {
    // 通过事件总线传值
    uni.$emit('selectAddress', address)

    // 返回上一页
    uni.navigateBack()
  }
}

// 编辑地址
const editAddress = address => {
  uni.navigateTo({
    url: `/pages/user/address-edit?id=${address.id}&from=${fromPage.value}`
  })
}

// 删除地址
const deleteAddress = address => {
  currentAddress.value = address

  // 使用 MessageBox 替代 wd-dialog
  uni.showModal({
    title: '删除地址',
    content: '确定要删除该地址吗？',
    success: res => {
      if (res.confirm) {
        confirmDelete()
      }
    }
  })
}

// 确认删除
const confirmDelete = () => {
  if (!currentAddress.value) return

  // 这里是删除逻辑，实际应调用接口
  const index = addressList.value.findIndex(item => item.id === currentAddress.value.id)
  if (index !== -1) {
    addressList.value.splice(index, 1)

    uni.showToast({
      title: '删除成功',
      icon: 'success'
    })
  }

  currentAddress.value = null
}

// 跳转到添加地址页面
const navigateToAddAddress = () => {
  uni.navigateTo({
    url: '/pages/user/address-edit'
  })
}

// 每次显示页面时更新地址列表
onShow(() => {
  // 实际应调用接口获取最新地址列表
})
</script>

<style>
.flex-col-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
</style>
