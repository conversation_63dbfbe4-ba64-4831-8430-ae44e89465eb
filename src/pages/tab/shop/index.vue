<route lang="json5" type="tab">
{
  layout: 'theme',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '购物车'
  }
}
</route>

<template>
  <view class="shop px-10rpx">
    <!-- 使用z-paging包裹整个页面内容 -->
    <z-paging
      ref="mainPaging"
      v-model="recommendList"
      :fixed="true"
      :refresher-enabled="true"
      :show-scrollbar="false"
      :safe-area-inset-bottom="true"
      :auto-show-back-to-top="true"
      @query="queryList"
    >
      <!-- z-paging空状态提示 -->
      <template #empty>
        <view class="py-60rpx">
          <wd-status-tip image="content" tip="购物车还是空的" image-size="200rpx"></wd-status-tip>
        </view>
      </template>

      <!-- 顶部标题栏 -->
      <view class="top-bar top-0 z-10 flex items-center justify-between bg-[#F3F5F7] p-30rpx">
        <text class="text-32rpx font-bold">购物车</text>
        <view class="text-24rpx" @click="clearAllCart">全部清除</view>
      </view>

      <!-- 购物车商品列表 -->
      <view class="mx-20rpx mt-20rpx">
        <view
          v-for="(item, index) in cartList"
          :key="item.id"
          class="relative rounded-16rpx bg-white p-(x-22rpx y-26rpx)"
          :class="index !== cartList.length - 1 ? 'mb-30rpx' : ''"
        >
          <!-- 删除按钮 -->
          <view class="absolute right-26rpx top-10rpx z-10" @click="removeFromCart([item.product_id])">
            <text class="i-uiw-delete text-24rpx text-[#CCCCCC]"></text>
          </view>

          <view class="flex">
            <!-- 商品图片 -->
            <view class="mr-20rpx">
              <wd-img :src="item.image" width="150rpx" height="150rpx" radius="8rpx" />
            </view>

            <!-- 商品信息 -->
            <view class="flex flex-1 flex-col">
              <view class="line-clamp-2 pr-40rpx text-24rpx font-medium">{{ item.name }}</view>
              <view
                class="mt-10rpx inline-block h-34rpx self-start rounded-4rpx rounded-8rpx bg-[#F2F2F2] px-10rpx text-20rpx leading-34rpx"
              >
                {{ item.sku_name }}
              </view>

              <!-- 数量控制 -->
              <view class="mr-10rpx mt-4rpx h-36rpx flex items-center self-end leading-36rpx">
                <view class="flex-center" @click="handleQuantityChange(item, -1)">
                  <text class="text-26rpx text-[#000]" :class="item.quantity <= 1 ? 'text-[#ccc]' : ''">-</text>
                </view>
                <view class="mx-10rpx min-w-70rpx flex-center rounded-8rpx bg-[#F7F7F7] px-20rpx">
                  <text class="text-20rpx">{{ item.quantity }}</text>
                </view>
                <view class="flex-center" @click="handleQuantityChange(item, 1)">
                  <text class="text-26rpx text-[#000]">+</text>
                </view>
              </view>

              <!-- 价格和立即购买 -->
              <view class="mt-22rpx flex items-center justify-between">
                <view class="text-28rpx text-primary font-bold">¥{{ item.price }}</view>

                <!-- 立即购买按钮 -->
                <wd-button
                  class="btn-primary !h-52rpx !min-w-auto !w-138rpx !rounded-10rpx !text-24rpx"
                  @click="buyNow(item)"
                >
                  立即购买
                </wd-button>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 商品推荐 -->
      <view class="my-30rpx px-20rpx">
        <view v-if="recommendList.length > 0">
          <goods-recommendation ref="goodsRecommendationRef"></goods-recommendation>
        </view>
        <view v-else class="py-60rpx">
          <wd-status-tip tip="暂无推荐商品" image-size="160"></wd-status-tip>
        </view>
      </view>

      <!-- 自定义底部加载更多区域 -->
      <template #loadingMore>
        <view class="flex-center py-30rpx">
          <view v-if="mainPaging?.isLoading" class="flex-center">
            <wd-loading size="30rpx" color="#999999" class="mr-10rpx"></wd-loading>
            <text class="text-24rpx text-[#999999]">正在加载更多...</text>
          </view>
          <text v-else-if="mainPaging?.isComplete" class="text-24rpx text-[#999999]">— 已加载全部 —</text>
        </view>
      </template>
    </z-paging>
  </view>

  <!-- 底部结算栏 -->
  <view
    v-if="cartList.length > 0"
    class="fixed bottom-[calc(var(--window-bottom)-1rpx)] left-0 right-0 z-999 h-100rpx flex items-center bg-white px-30rpx"
  >
    <view class="flex items-center" @click="toggleSelectAll">
      <text
        :class="isAllSelected ? 'i-uiw-check-o text-[#FF3C29]' : 'i-uiw-circle-o text-[#CCCCCC]'"
        class="mr-10rpx text-40rpx"
      ></text>
      <text class="text-28rpx">全选</text>
    </view>

    <view class="flex flex-1 items-center justify-end">
      <view class="mr-30rpx">
        <text class="text-26rpx text-[#333333]">合计：</text>
        <text class="text-32rpx text-[#FF3C29] font-bold">¥{{ totalPrice }}</text>
      </view>

      <view
        class="h-70rpx flex-center rounded-35rpx px-40rpx"
        :class="selectedCount > 0 ? 'bg-[#FF3C29] text-white' : 'bg-[#CCCCCC] text-[#FFFFFF]'"
        @click="checkout"
      >
        <text class="text-28rpx">结算({{ selectedCount }})</text>
      </view>
    </view>
  </view>

  <wd-message-box></wd-message-box>
</template>

<script setup lang="ts">
import { useCart } from '@/hooks/useCart'
import { useMessage } from 'wot-design-uni'

const message = useMessage()

defineOptions({
  name: 'CartPage'
})

const { cartList, fetchCartList, removeFromCart, clearCart, updateCartQuantity } = useCart()

// 获取购物车列表
fetchCartList()

const goodsRecommendationRef = ref(null)

// 推荐商品列表 - 由z-paging管理
const recommendList = ref([])

// z-paging组件引用
const mainPaging = ref(null)

// 分页相关
const currentPage = ref(1)
const totalPages = ref(5)

// 不需要手动刷新函数，z-paging的@query事件会自动处理刷新和加载更多

// 滚动到底部触发加载更多
const onScrollToLower = () => {
  // 不需要额外处理，z-paging会自动触发@query事件
}

// 查询列表数据 - z-paging @query事件会触发此方法
const queryList = async (pageNo: number, pageSize: number) => {
  try {
    // 模拟网络请求
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 模拟请求到的数据
    const items = []
    // 每次加载多个商品
    for (let i = 0; i < pageSize; i++) {
      items.push({
        id: pageNo * 10 + i,
        image: '/static/images/home/<USER>',
        title: `推荐商品${pageNo}-${i + 1}`,
        price: (Math.random() * 200 + 99).toFixed(2),
        sold: Math.floor(Math.random() * 10000) + '+'
      })
    }

    // 更新页码
    currentPage.value = pageNo

    // 判断是否还有更多数据
    if (pageNo >= totalPages.value) {
      // 没有更多数据了
      mainPaging.value.complete(items)
    } else {
      // 还有更多数据
      mainPaging.value.complete(items)
    }
  } catch (error) {
    console.error('加载数据失败', error)
    mainPaging.value.complete(false)
  }
}

// 计算属性：是否全选
const isAllSelected = computed(() => {
  return cartList.value.length > 0 && cartList.value.every(item => item.selected)
})

// 计算属性：选中的商品数量
const selectedCount = computed(() => {
  return cartList.value.filter(item => item.selected).length
})

// 计算属性：总价
const totalPrice = computed(() => {
  return cartList.value
    .filter(item => item.selected)
    .reduce((total, item) => total + Number(item.price) * item.quantity, 0)
    .toFixed(2)
})

// 切换商品选中状态
const toggleSelect = item => {
  item.selected = !item.selected
}

// 切换全选状态
const toggleSelectAll = () => {
  const newStatus = !isAllSelected.value
  cartList.value.forEach(item => {
    item.selected = newStatus
  })
}

// 增加商品数量
const increaseQuantity = item => {
  item.quantity++
}

// 减少商品数量
const decreaseQuantity = item => {
  if (item.quantity > 1) {
    item.quantity--
  } else {
    message
      .confirm({
        title: '提示',
        msg: '确定要减少该商品吗？'
      })
      .then(() => {
        removeFromCart([item.id])
      })
      .catch(() => {
        console.log('取消')
      })
  }
}

// 处理商品数量更新
const handleQuantityChange = (item, quantity) => {
  console.log(`item -->`, item)
  console.log(`quantity -->`, quantity)
  item.quantity += quantity
  // updateCartQuantity(item.id, quantity)
}

// 从购物车移除商品
// const removeFromCart = item => {
//   uni.showModal({
//     title: '提示',
//     content: '确定要移除该商品吗？',
//     confirmColor: '#FF3C29',
//     rounded: '16rpx',
//     success: res => {
//       if (res.confirm) {
//         cartList.value = cartList.value.filter(cartItem => cartItem.id !== item.id)
//       }
//     }
//   })
// }

// 立即购买
const buyNow = item => {
  uni.navigateTo({
    url: `/pages/shop/checkout?productId=${item.id}&quantity=${item.quantity}`
  })
}

// 清空购物车
const clearAllCart = () => {
  if (cartList.value.length === 0) return

  message
    .confirm({
      title: '提示',
      msg: '确定要清空购物车吗？',
      zIndex: 99999
    })
    .then(() => {
      clearCart()
    })
}

// 去结算
const checkout = () => {
  if (selectedCount.value === 0) return

  uni.navigateTo({
    url: '/pages/shop/checkout'
  })
}

// 去商城
const goToShop = () => {
  uni.switchTab({
    url: '/pages/tab/shop/index'
  })
}

// 处理商品点击
const handleGoodsClick = goods => {
  uni.navigateTo({
    url: `/pages/shop/shop-detail?id=${goods.id}`
  })
}
</script>

<style lang="scss" scoped>
.top-bar {
  /* #ifdef APP-PLUS */
  margin-top: calc(var(--status-bar-height) + 30rpx);
  /* #endif */
}
</style>
