<route lang="json5" type="page">
{
  layout: 'theme',
  style: {
    navigationBarTitleText: '商品列表',
    navigationStyle: 'custom'
  }
}
</route>

<template>
  <view class="shop-list min-h-100vh flex flex-col bg-white">
    <!-- 商品列表 -->
    <z-paging
      ref="paging"
      v-model="productList"
      @query="queryProductList"
      :fixed="true"
      :refresher-enabled="true"
      :auto-show-back-to-top="true"
      :show-scrollbar="false"
      class="product-list w-full flex-1 px-30rpx py-30rpx"
      :style="{ marginTop: 'calc(var(--status-bar-height) + 180rpx)' }"
    >
      <template #top>
        <!-- 自定义导航栏 -->
        <view
          class="fixed left-0 right-0 top-0 z-100 flex items-center bg-white"
          style="height: calc(100rpx + var(--status-bar-height)); padding-top: var(--status-bar-height)"
        >
          <view class="ml-20rpx h-60rpx w-60rpx flex items-center justify-center" @click="handleBack">
            <wd-icon name="arrow-left" size="40rpx" color="#333" />
          </view>
          <view class="ml-20rpx flex-1">
            <wd-search
              v-model="searchKeyword"
              placeholder="搜索商品"
              hide-cancel
              custom-class="shop-search"
              :clearable="true"
              @search="onSearch"
            />
          </view>
        </view>

        <!-- 排序栏 -->
        <view
          class="fixed left-0 right-0 z-99 h-90rpx flex items-center justify-around bg-white shadow-sm"
          style="top: calc(100rpx + var(--status-bar-height))"
        >
          <view
            v-for="(item, index) in sortOptions"
            :key="index"
            class="h-90rpx flex items-center justify-center text-26rpx"
            :class="currentSort === index ? 'font-500  text-[var(--primary-color)]' : 'text-[#7a7a7a]'"
            @click="handleSort(index)"
          >
            <text>{{ item.label }}</text>
            <view v-if="item.hasArrow" class="ml-6rpx mt-5rpx flex flex-col">
              <wd-icon
                class="mb--5rpx"
                name="arrow-up"
                size="24rpx"
                :color="currentSort === index && sortOrder === 'ASC' ? 'var(--primary-color)' : '#7A7A7A'"
              />
              <wd-icon
                class="mt--5rpx"
                name="arrow-down"
                size="24rpx"
                :color="currentSort === index && sortOrder === 'DESC' ? 'var(--primary-color)' : '#7A7A7A'"
              />
            </view>
          </view>
        </view>
      </template>

      <view class="w-full flex flex-col">
        <view
          v-for="(item, index) in productList"
          :key="index"
          class="mb-20rpx w-full flex border-b border-b-[#F0F0F0] bg-white"
          @click="navToDetail(item)"
          style="border-bottom: 1px solid #f0f0f0"
        >
          <image class="h-250rpx w-250rpx flex-shrink-0 p-15rpx" :src="item.main_image" mode="aspectFill" />
          <view class="relative flex flex-1 flex-col p-20rpx">
            <view
              class="line-clamp-3 overflow-hidden text-26rpx text-[#333]"
              style="max-height: 80rpx; line-height: 1.4"
            >
              {{ item.name }}
            </view>
            <view class="mt-auto flex items-baseline text-[var(--primary-color)]">
              <text class="text-34rpx">¥</text>
              <text class="text-34rpx font-500">{{ item.min_price.toFixed(2) }}</text>
            </view>
            <view class="absolute bottom-20rpx right-20rpx">
              <image src="../../static/images/home/<USER>" class="h-38rpx w-38rpx" mode="aspectFit"></image>
            </view>
          </view>
        </view>
      </view>
    </z-paging>
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { onPageScroll, onLoad } from '@dcloudio/uni-app'
import { getGoodsListApi } from '@/api'

const categoryId = ref(0)
const categoryLevel = ref(0)

onLoad(options => {
  // 从页面参数获取分类信息
  if (options.categoryId) {
    categoryId.value = options.categoryId
    categoryLevel.value = options.categoryLevel
  }
})

// 搜索关键词
const searchKeyword = ref('')
// 排序状态
const currentSort = ref(0)
const sortOrder = ref('DESC')
// 排序选项
const sortOptions = reactive([
  { label: '综合排序', hasArrow: false },
  { label: '销量', hasArrow: false },
  { label: '价格', hasArrow: true }
])

// 商品列表
const productList = ref<any[]>([])
const paging = ref<any>(null)

// 页面滚动位置
const scrollTop = ref(0)

// 处理返回
const handleBack = () => {
  uni.navigateBack()
}

// 处理搜索
const onSearch = () => {
  paging.value.reload()
}

// 处理排序
const handleSort = (index: number) => {
  console.log(` -->`, index)
  if (index === currentSort.value && sortOptions[index].hasArrow) {
    // 如果点击的是当前排序项且有箭头，切换排序方向
    sortOrder.value = sortOrder.value === 'ASC' ? 'DESC' : 'ASC'
  } else {
    // 切换排序项
    currentSort.value = index
    // 价格排序默认降序
    if (index === 2) {
      sortOrder.value = 'DESC'
    }
  }
  // 重新加载数据
  paging.value.reload()
}

// 页面滚动监听
onPageScroll(e => {
  scrollTop.value = e.scrollTop
})

// 获取商品列表数据
const queryProductList = async (pageNo: number, pageSize: number) => {
  try {
    // 模拟接口请求
    const [res] = await getGoodsListApi({
      category_id: categoryId.value,
      level: categoryLevel.value,
      // is_hot: 1,
      sort: 'created_at',
      order: 'DESC',
      page: pageNo,
      size: pageSize
    })

    // 返回数据
    paging.value.complete(res?.data || [])
  } catch (e) {
    // 异常处理
    paging.value.complete(false)
  }
}

// 跳转商品详情
const navToDetail = (item: any) => {
  uni.navigateTo({
    url: `/pages/shop/shop-detail?id=${item.id}`
  })
}
</script>

<style lang="scss" scoped>
:deep(.wd-search) {
  --wd-search-padding: 0 20rpx;
  --wd-search-input-height: 70rpx;
  --wd-search-bg-color: #f5f5f5;
  --wd-search-input-border-radius: 35rpx;

  .wd-search__search-icon {
    font-size: unset;
  }
  .wd-search__cover {
    height: auto;
    line-height: var(--wot-search-input-height, 50rpx);
  }
}
</style>
