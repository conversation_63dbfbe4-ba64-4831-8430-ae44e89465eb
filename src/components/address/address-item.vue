<template>
  <view class="address-item rounded-13rpx bg-white p-30rpx">
    <view class="flex items-center justify-between">
      <view class="flex flex-1 items-center" @click="handleClick">
        <image src="/static/images/shop/address-icon.png" class="mr-20rpx h-50rpx w-50rpx" mode="aspectFit" />
        <view class="flex-1" v-if="address">
          <view class="flex items-baseline">
            <view class="line-clamp-2 flex-auto text-30rpx text-gray-800 font-medium">{{ address.fullAddress }}</view>
            <view
              v-if="address.isDefault"
              class="ml-20rpx w-100rpx rounded-8rpx bg-[#FFF2F2] p-(x-10rpx y-4rpx) text-(center 22rpx #FF4D4F)"
            >
              默认
            </view>
          </view>
          <view class="mt-10rpx flex items-center">
            <view class="text-25rpx text-[#999]">{{ address.name }}</view>
            <view class="ml-20rpx text-25rpx text-[#999]">{{ address.phone }}</view>
          </view>
        </view>
        <view v-else class="text-30rpx text-gray-600">添加收货地址</view>
      </view>

      <wd-icon @click="handleClick" v-if="mode === 'select'" name="arrow-right" size="32rpx" color="#999" />

      <wd-icon v-else @click="editAddress" name="edit-outline" size="32rpx" color="#999" />
    </view>
  </view>
</template>

<script lang="ts" setup>
const props = defineProps({
  address: {
    type: Object,
    default: null
  },
  mode: {
    type: String,
    default: 'select' // select: 选择模式, manage: 管理模式
  }
})

const emit = defineEmits(['click', 'edit', 'delete', 'setDefault'])

const handleClick = () => {
  emit('click', props.address)
}

function editAddress() {
  console.log(`editAddress -->`)
  emit('edit', props.address)
}
</script>

<style>
.address-item {
  position: relative;
  overflow: hidden;
}

.address-item::before {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  height: 10rpx;
  content: '';
  background-image: linear-gradient(
    45deg,
    #fff 25%,
    transparent 25%,
    transparent 50%,
    #fff 50%,
    #fff 75%,
    transparent 75%,
    transparent
  );
  background-size: 20rpx 20rpx;
  opacity: 0.5;
}
</style>
