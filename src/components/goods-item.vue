<template>
  <view class="box-border h-475rpx flex flex-col overflow-hidden rounded-13rpx bg-white px-13rpx">
    <wd-img :src="goods?.main_image" mode="aspectFit" class="mb-18rpx h-337rpx w-337rpx" />
    <view class="mx-23rpx h-35rpx text-left text-22rpx font-400 leading-35rpx">
      {{ goods?.name }}
    </view>
    <view class="mx-23rpx h-36rpx text-26rpx text-[#FF3C29] font-bold leading-36rpx">￥{{ goods?.min_price }}</view>
    <view
      class="pos-relative mx-23rpx mt-5rpx h-35rpx flex justify-between text-left text-22rpx font-400 leading-35rpx"
    >
      <view class="text-15rpx text-[#999999]">已售：{{ goods?.sales_volume }}</view>
      <!-- 添加到购物车 -->
      <view class="mt--10rpx text-[#969696]" @click.stop="handleAddToCart(goods)">
        <wd-icon name="/static/images/home/<USER>" size="33rpx"></wd-icon>
      </view>
    </view>
  </view>
</template>

<script lang="ts">
// 导入defineComponent
import { defineComponent } from 'vue'

// 标准的组件定义，提供默认导出
export default defineComponent({
  name: 'GoodsItem'
})
</script>

<script setup lang="ts">
import { getGoodsDetailApi } from '@/api'
import { useCart } from '@/hooks/useCart'

// 定义组件名称
defineOptions({
  name: 'GoodsItem'
})

// 属性定义
defineProps({
  goods: {
    type: Object as () => {
      id?: number | string
      main_image: string
      min_price: number
      sales_volume: number
      name: string
      [key: string]: any
    },
    required: true
  }
})

// 获取购物车相关方法
const { addToCart, loading } = useCart()

// 添加商品到购物车
const handleAddToCart = async (goods: Record<string, any>) => {
  if (!goods?.id) return

  let [goods_detail, err] = await getGoodsDetailApi({ id: goods.id })
  if (err) {
    Utils.toast(err.message)
    return
  }

  let sku_id = goods_detail?.data?.skus?.[0]?.id
  console.log(`sku_id -->`, sku_id)

  // 使用购物车hook添加商品
  await addToCart({ product_id: goods.id, quantity: 1, sku_id }, true)
}
</script>

<style scoped>
/* 商品组件样式 */
</style>
