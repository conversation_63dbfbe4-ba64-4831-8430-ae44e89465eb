<!-- full.vue -->
<template>
  <WdConfigProvider :theme="theme" :theme-vars="themeVars">
    <view class="full-layout">
      <!-- 沉浸式导航栏背景 -->
      <view class="full-layout__header-bg">
        <slot name="header-bg">
          <!-- 默认顶部背景 -->
        </slot>
      </view>

      <!-- 顶部区域：状态栏 + navbar + 自定义顶部内容 -->
      <view class="full-layout__header">
        <template v-if="pageConfig?.style?.customNav">
          <view class="full-layout__header-nav">
            <wd-navbar
              :title="pageConfig?.style?.navigationBarTitleText"
              :bordered="false"
              left-arrow
              @click-left="onBack"
              custom-style="background-color: transparent !important;"
            >
              <template #left>
                <svg width="26" height="26" viewBox="0 0 32 32">
                  <path
                    d="M21.781 7.844l-9.063 8.594 9.063 8.594q0.25 0.25 0.25 0.609t-0.25 0.578q-0.25 0.25-0.578 0.25t-0.578-0.25l-9.625-9.125q-0.156-0.125-0.203-0.297t-0.047-0.359q0-0.156 0.047-0.328t0.203-0.297l9.625-9.125q0.25-0.25 0.578-0.25t0.578 0.25q0.25 0.219 0.25 0.578t-0.25 0.578z"
                    fill="#000000"
                  ></path>
                </svg>
              </template>
            </wd-navbar>
          </view>
        </template>
        <template v-else>
          <slot name="header">
            <!-- 默认顶部内容 -->
          </slot>
        </template>
      </view>

      <!-- 中间内容区域：自动填充剩余空间 -->
      <view class="full-layout__content">
        <slot></slot>
      </view>

      <!-- 底部区域：操作按钮 + tabbar + 安全距离 -->
      <view class="full-layout__footer">
        <slot name="footer">
          <!-- 默认底部内容 -->
        </slot>
      </view>
    </view>
    <wd-toast />
  </WdConfigProvider>
</template>

<script setup lang="ts">
import { useTheme } from '@/hooks/useTheme'
import { usePageConfig } from '@/hooks/usePageConfig'

const { theme, themeVars } = useTheme()
const { pageConfig } = usePageConfig()

function onBack() {
  uni.navigateBack()
}

onMounted(() => {
  console.log('当前页面pages.json配置:', pageConfig.value)
})
</script>

<style lang="scss">
.full-layout {
  @apply flex flex-col h-[calc(100vh-var(--window-top)-var(--window-bottom))] overflow-hidden;

  &__header-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: var(--window-top);
  }

  &__header {
    z-index: 100;
    width: 100%;
  }

  &__content {
    flex: 1;
    width: 100%;
    overflow: auto;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */

    /* 隐藏滚动条 */
    &::-webkit-scrollbar {
      display: none;
      width: 0;
      height: 0;
      background: transparent;
    }
  }

  &__footer {
    z-index: 100;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    padding: 15rpx 15rpx calc(var(--window-bottom) + 15rpx);
    background-color: #fff;
  }
  &__header-nav {
    :deep(.wd-navbar__left) {
      padding: 0 10rpx !important;
    }

    :deep(.wd-navbar__title) {
      font-size: 32rpx !important;
      font-weight: 400 !important;
    }
  }
}
</style>
