/**
 * HTTP 请求封装
 *
 * 这个模块封装了 HTTP 请求，提供了两种使用方式：
 * 1. await-to-js 风格：const [res, err] = await http.get('/url')
 * 2. 直接 await 风格：const res = await http.get('/url').unwrap()
 *
 * 两种方式都能获取完整的响应对象（包含 code、msg/message、data）
 */

import ApiClient from '@/http/httpClient'
import type { CustomRequestOptions } from '@/interceptors/request'

// 从 CustomRequestOptions 中提取出需要的类型
type RequestPayload = Omit<CustomRequestOptions, 'url' | 'method'>

/**
 * 标准 API 响应类型
 */
export interface ApiResponse<T = any> {
  code: number
  msg?: string
  message?: string
  data: T
}

/**
 * 扩展的 Promise 类型
 * 既可以当作 Promise<[T | null, any, number?, string?]> 使用（await-to-js 风格）
 * 也可以通过 .unwrap() 方法当作 Promise<T> 使用（直接 await 风格）
 */
export interface AwaitToPromise<T> extends Promise<[T | null, any, number?, string?]> {
  // 保留原始 Promise 的方法
  then<TResult1 = T, TResult2 = never>(
    onfulfilled?: ((value: [T | null, any, number?, string?]) => TResult1 | PromiseLike<TResult1>) | undefined | null,
    onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null
  ): Promise<TResult1 | TResult2>

  catch<TResult = never>(
    onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null
  ): Promise<[T | null, any, number?, string?] | TResult>

  // 添加 unwrap 方法，用于直接获取数据（如果有错误则抛出异常）
  unwrap(): Promise<T>
}

/**
 * 创建一个包装函数，将普通 Promise 转换为 AwaitToPromise
 * @param promise 原始的 Promise 对象
 * @returns 包装后的 AwaitToPromise 对象
 */
function wrapPromise<T>(promise: Promise<T>): AwaitToPromise<T> {
  // 创建一个新的 Promise，将原始 Promise 的结果转换为 [data, error, code, msg] 格式
  const basePromise = promise.then(
    data => {
      // 成功情况：[data, null, code, message]
      const code = (data as any)?.code || 0
      const msg = (data as any)?.msg || (data as any)?.message || '成功'
      return [data, null, code, msg] as [T, null, number, string]
    },
    error => {
      // 处理错误情况，确保错误对象格式一致
      let code = -1
      let msg = '未知错误'

      // 如果错误对象是 HTTP 响应错误（包含 statusCode 等），则提取 code 和 message
      if (error && typeof error === 'object') {
        if ('data' in error && typeof error.data === 'object') {
          code = error.data?.code || -1
          msg = error.data?.msg || error.data?.message || '请求异常'
        } else if ('statusCode' in error) {
          code = error.statusCode || -1
          msg = '请求异常'
        } else if ('message' in error) {
          msg = error.message
        }
      }

      return [null, error, code, msg] as [null, any, number, string]
    }
  )

  // 使用 as unknown as 转换类型，解决 TypeScript 类型检查问题
  const wrappedPromise = basePromise as unknown as AwaitToPromise<T>

  // 添加 unwrap 方法
  wrappedPromise.unwrap = async function () {
    const [data, error] = await basePromise
    if (error) throw error
    return data!
  }

  return wrappedPromise
}

/**
 * GET 请求
 * @param url 请求地址
 * @param options 请求选项
 * @returns AwaitToPromise 对象
 */
export function get<T = any>(url: string, options?: RequestPayload): AwaitToPromise<ApiResponse<T>> {
  return wrapPromise(ApiClient.get<ApiResponse<T>>(url, options))
}

/**
 * POST 请求
 * @param url 请求地址
 * @param options 请求选项
 * @returns AwaitToPromise 对象
 */
export function post<T = any>(url: string, options?: RequestPayload): AwaitToPromise<ApiResponse<T>> {
  return wrapPromise(ApiClient.post<ApiResponse<T>>(url, options))
}

/**
 * PUT 请求
 * @param url 请求地址
 * @param options 请求选项
 * @returns AwaitToPromise 对象
 */
export function put<T = any>(url: string, options?: RequestPayload): AwaitToPromise<ApiResponse<T>> {
  return wrapPromise(ApiClient.put<ApiResponse<T>>(url, options))
}

/**
 * DELETE 请求
 * @param url 请求地址
 * @param options 请求选项
 * @returns AwaitToPromise 对象
 */
export function del<T = any>(url: string, options?: RequestPayload): AwaitToPromise<ApiResponse<T>> {
  return wrapPromise(ApiClient.delete<ApiResponse<T>>(url, options))
}

// 导出一个默认对象，包含所有方法
const http = {
  get,
  post,
  put,
  delete: del
}

export default http
